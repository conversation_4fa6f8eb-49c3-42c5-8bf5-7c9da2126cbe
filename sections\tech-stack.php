<!-- TECH STACK VISUALIZATION -->
<section id="tech-stack" class="loading">
  <div class="wrap">
    <div class="section-header">
      <h2>Tech Stack Universe</h2>
      <p class="muted">Technologies orbiting in my development ecosystem</p>
    </div>
    
    <div class="tech-universe">
      <div class="phoenix-core" id="phoenixCore">
        <div class="core-glow"></div>
        <div class="core-symbol">🔥</div>
        <div class="core-label">Backend<br>Developer</div>
      </div>
      
      <!-- Primary Orbit - Core Technologies -->
      <div class="orbit orbit-primary" data-orbit="primary">
        <div class="tech-planet" data-tech="python" style="--angle: 0deg;">
          <div class="planet-icon">🐍</div>
          <div class="planet-label">Python</div>
          <div class="planet-glow"></div>
        </div>
        <div class="tech-planet" data-tech="django" style="--angle: 90deg;">
          <div class="planet-icon">🎸</div>
          <div class="planet-label">Django</div>
          <div class="planet-glow"></div>
        </div>
        <div class="tech-planet" data-tech="php" style="--angle: 180deg;">
          <div class="planet-icon">🐘</div>
          <div class="planet-label">PHP</div>
          <div class="planet-glow"></div>
        </div>
        <div class="tech-planet" data-tech="mysql" style="--angle: 270deg;">
          <div class="planet-icon">🗄️</div>
          <div class="planet-label">MySQL</div>
          <div class="planet-glow"></div>
        </div>
      </div>
      
      <!-- Secondary Orbit - Frontend & Tools -->
      <div class="orbit orbit-secondary" data-orbit="secondary">
        <div class="tech-planet" data-tech="javascript" style="--angle: 45deg;">
          <div class="planet-icon">⚡</div>
          <div class="planet-label">JavaScript</div>
          <div class="planet-glow"></div>
        </div>
        <div class="tech-planet" data-tech="bootstrap" style="--angle: 135deg;">
          <div class="planet-icon">🎨</div>
          <div class="planet-label">Bootstrap</div>
          <div class="planet-glow"></div>
        </div>
        <div class="tech-planet" data-tech="git" style="--angle: 225deg;">
          <div class="planet-icon">🌿</div>
          <div class="planet-label">Git</div>
          <div class="planet-glow"></div>
        </div>
        <div class="tech-planet" data-tech="docker" style="--angle: 315deg;">
          <div class="planet-icon">🐳</div>
          <div class="planet-label">Docker</div>
          <div class="planet-glow"></div>
        </div>
      </div>
      
      <!-- Outer Orbit - Additional Tools -->
      <div class="orbit orbit-tertiary" data-orbit="tertiary">
        <div class="tech-planet" data-tech="linux" style="--angle: 30deg;">
          <div class="planet-icon">🐧</div>
          <div class="planet-label">Linux</div>
          <div class="planet-glow"></div>
        </div>
        <div class="tech-planet" data-tech="postman" style="--angle: 120deg;">
          <div class="planet-icon">📮</div>
          <div class="planet-label">Postman</div>
          <div class="planet-glow"></div>
        </div>
        <div class="tech-planet" data-tech="vscode" style="--angle: 210deg;">
          <div class="planet-icon">💻</div>
          <div class="planet-label">VS Code</div>
          <div class="planet-glow"></div>
        </div>
        <div class="tech-planet" data-tech="aws" style="--angle: 300deg;">
          <div class="planet-icon">☁️</div>
          <div class="planet-label">AWS</div>
          <div class="planet-glow"></div>
        </div>
      </div>
      
      <!-- Floating Asteroids - Additional Skills -->
      <div class="asteroid" data-skill="ajax" style="--x: 15%; --y: 20%;">
        <span class="asteroid-label">Ajax</span>
      </div>
      <div class="asteroid" data-skill="api" style="--x: 85%; --y: 30%;">
        <span class="asteroid-label">REST API</span>
      </div>
      <div class="asteroid" data-skill="responsive" style="--x: 20%; --y: 80%;">
        <span class="asteroid-label">Responsive</span>
      </div>
      <div class="asteroid" data-skill="optimization" style="--x: 80%; --y: 75%;">
        <span class="asteroid-label">Optimization</span>
      </div>
    </div>
    
    <div class="tech-details">
      <div class="tech-categories">
        <div class="category-card" data-category="backend">
          <h3>Backend Mastery</h3>
          <p>Building robust server-side applications with Python, Django, and PHP. Expert in database design, API development, and system architecture.</p>
          <div class="category-skills">
            <span class="skill-tag primary">Python</span>
            <span class="skill-tag primary">Django</span>
            <span class="skill-tag primary">PHP</span>
            <span class="skill-tag primary">MySQL</span>
          </div>
        </div>
        
        <div class="category-card" data-category="frontend">
          <h3>Frontend Integration</h3>
          <p>Creating seamless user experiences with modern JavaScript, responsive design, and efficient frontend-backend communication.</p>
          <div class="category-skills">
            <span class="skill-tag secondary">JavaScript</span>
            <span class="skill-tag secondary">Bootstrap</span>
            <span class="skill-tag secondary">Ajax</span>
            <span class="skill-tag secondary">HTML/CSS</span>
          </div>
        </div>
        
        <div class="category-card" data-category="devops">
          <h3>DevOps & Tools</h3>
          <p>Streamlining development workflows with version control, containerization, and cloud deployment solutions.</p>
          <div class="category-skills">
            <span class="skill-tag tertiary">Git</span>
            <span class="skill-tag tertiary">Docker</span>
            <span class="skill-tag tertiary">Linux</span>
            <span class="skill-tag tertiary">AWS</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
