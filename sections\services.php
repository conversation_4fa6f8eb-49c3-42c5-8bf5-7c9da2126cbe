<!-- SERVICES -->
<section id="services" class="loading">
  <div class="wrap">
    <div class="section-header">
      <h2>Freelance Services</h2>
      <p class="muted">Comprehensive backend development solutions tailored to your business needs</p>
    </div>
    
    <div class="services-grid">
      <article class="service-card enhanced-hover parallax-element" data-service="backend">
        <div class="service-icon">🚀</div>
        <h3>Backend Development</h3>
        <p>Robust server-side applications, RESTful APIs, and business logic implementation using Python, Django, and PHP.</p>
        <ul class="service-features">
          <li>Custom API development</li>
          <li>Database architecture</li>
          <li>Authentication systems</li>
          <li>Performance optimization</li>
        </ul>
        <div class="service-tech">
          <span class="tech-tag">Python</span>
          <span class="tech-tag">Django</span>
          <span class="tech-tag">PHP</span>
          <span class="tech-tag">MySQL</span>
        </div>
      </article>

      <article class="service-card enhanced-hover parallax-element" data-service="fullstack">
        <div class="service-icon">🌐</div>
        <h3>Full-Stack Solutions</h3>
        <p>Complete web applications from frontend interfaces to backend systems, ensuring seamless integration and user experience.</p>
        <ul class="service-features">
          <li>End-to-end development</li>
          <li>Responsive design</li>
          <li>Frontend-backend integration</li>
          <li>User experience optimization</li>
        </ul>
        <div class="service-tech">
          <span class="tech-tag">JavaScript</span>
          <span class="tech-tag">Bootstrap</span>
          <span class="tech-tag">Ajax</span>
          <span class="tech-tag">HTML/CSS</span>
        </div>
      </article>

      <article class="service-card enhanced-hover parallax-element" data-service="database">
        <div class="service-icon">🗄️</div>
        <h3>Database Design & Optimization</h3>
        <p>Efficient database schemas, query optimization, and data management solutions for scalable applications.</p>
        <ul class="service-features">
          <li>Schema design</li>
          <li>Query optimization</li>
          <li>Data migration</li>
          <li>Performance tuning</li>
        </ul>
        <div class="service-tech">
          <span class="tech-tag">MySQL</span>
          <span class="tech-tag">Database Design</span>
          <span class="tech-tag">Indexing</span>
          <span class="tech-tag">Optimization</span>
        </div>
      </article>

      <article class="service-card enhanced-hover parallax-element" data-service="integration">
        <div class="service-icon">🔗</div>
        <h3>API Integration & Automation</h3>
        <p>Seamless third-party integrations, webhook implementations, and automated workflows to streamline business processes.</p>
        <ul class="service-features">
          <li>REST API integration</li>
          <li>Webhook development</li>
          <li>Process automation</li>
          <li>Data synchronization</li>
        </ul>
        <div class="service-tech">
          <span class="tech-tag">REST APIs</span>
          <span class="tech-tag">Webhooks</span>
          <span class="tech-tag">Automation</span>
          <span class="tech-tag">Integration</span>
        </div>
      </article>

      <article class="service-card enhanced-hover parallax-element" data-service="deployment">
        <div class="service-icon">☁️</div>
        <h3>Deployment & Hosting</h3>
        <p>Professional deployment solutions, server configuration, and ongoing maintenance for reliable application hosting.</p>
        <ul class="service-features">
          <li>Server setup & configuration</li>
          <li>Docker containerization</li>
          <li>Cloud deployment</li>
          <li>Monitoring & maintenance</li>
        </ul>
        <div class="service-tech">
          <span class="tech-tag">Docker</span>
          <span class="tech-tag">Linux</span>
          <span class="tech-tag">AWS</span>
          <span class="tech-tag">VPS</span>
        </div>
      </article>

      <article class="service-card cta-card enhanced-hover" data-service="custom">
        <div class="service-icon">💡</div>
        <h3>Custom Solutions</h3>
        <p>Have a unique project in mind? Let's discuss your specific requirements and create a tailored solution.</p>
        <div class="cta-content">
          <p class="cta-text">Ready to start your project?</p>
          <a href="#contact" class="btn primary enhanced-hover">
            <span class="btn-icon">💬</span>
            Let's Talk
          </a>
        </div>
      </article>
    </div>

    <div class="services-footer">
      <div class="process-overview">
        <h3>My Development Process</h3>
        <div class="process-steps">
          <div class="process-step">
            <span class="step-number">1</span>
            <div class="step-content">
              <h4>Discovery</h4>
              <p>Understanding your requirements and goals</p>
            </div>
          </div>
          <div class="process-step">
            <span class="step-number">2</span>
            <div class="step-content">
              <h4>Planning</h4>
              <p>Technical architecture and project roadmap</p>
            </div>
          </div>
          <div class="process-step">
            <span class="step-number">3</span>
            <div class="step-content">
              <h4>Development</h4>
              <p>Agile development with regular updates</p>
            </div>
          </div>
          <div class="process-step">
            <span class="step-number">4</span>
            <div class="step-content">
              <h4>Delivery</h4>
              <p>Testing, deployment, and ongoing support</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
