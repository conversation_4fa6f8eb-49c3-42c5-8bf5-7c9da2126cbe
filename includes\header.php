<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Apoorv Deep Sahu – Backend Developer Portfolio</title>
  <meta name="description" content="Backend Developer with hands-on experience building full-stack web applications, ERP systems, and CMS platforms. Skilled in PHP, Django, JavaScript, and MySQL." />
  <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
  <link rel="preconnect" href="https://unpkg.com" crossorigin>
  <!-- Phoenix favicon -->
  <link rel="icon" href="assets/img/phoenix.svg" type="image/svg+xml">
  <link rel="alternate icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔥</text></svg>">

  <!-- Google Analytics (replace GA_MEASUREMENT_ID with your actual ID) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'GA_MEASUREMENT_ID');
  </script>
  
  <!-- Lenis CSS -->
  <link rel="stylesheet" href="https://unpkg.com/lenis@1.3.8/dist/lenis.css">

  <!-- Google reCAPTCHA v3 (only load if configured) -->
  <?php if (getConfig('recaptcha.enabled', false) && getConfig('recaptcha.site_key')): ?>
  <script src="https://www.google.com/recaptcha/api.js?render=<?php echo htmlspecialchars(getConfig('recaptcha.site_key')); ?>" async defer></script>
  <?php endif; ?>

  <!-- Pass PHP config to JavaScript -->
  <script>
    // Set reCAPTCHA configuration from PHP
    window.RECAPTCHA_CONFIG = {
      enabled: <?php echo getConfig('recaptcha.enabled', false) ? 'true' : 'false'; ?>,
      version: 'v3',
      siteKey: '<?php echo htmlspecialchars(getConfig('recaptcha.site_key', '')); ?>',
      action: 'contact_form',
      scoreThreshold: <?php echo getConfig('recaptcha.score_threshold', 0.5); ?>
    };
  </script>

  <!-- Google Forms Integration -->
  <script src="config/google-forms.js"></script>

  <!-- CSS Styles -->
  <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
  <!-- Skip link for accessibility -->
  <a href="#main-content" class="skip-link">Skip to main content</a>

  <!-- Loading indicator -->
  <div id="loading-indicator" style="position: fixed; inset: 0; background: var(--bg); z-index: 1000; display: flex; align-items: center; justify-content: center; flex-direction: column; cursor: pointer;" onclick="this.style.display='none'; document.body.style.overflow='';">
    <div class="preloader-phoenix" style="width: 80px; height: 80px; animation: preloaderFly 2s ease-in-out infinite;">
      <img src="assets/img/phoenix.svg" alt="Loading..." style="width: 100%; height: 100%; filter: drop-shadow(0 0 20px rgba(255, 213, 107, 0.6));">
    </div>
    <p style="margin-top: 1rem; color: var(--muted);">Loading Portfolio...</p>
    <p style="margin-top: 0.5rem; color: var(--muted); font-size: 0.9rem; opacity: 0.7;">Click anywhere to continue</p>
  </div>

  <style>
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes preloaderFly {
      0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
      }
      25% {
        transform: translateY(-10px) rotate(5deg) scale(1.05);
      }
      50% {
        transform: translateY(-5px) rotate(-3deg) scale(1.02);
      }
      75% {
        transform: translateY(-15px) rotate(8deg) scale(1.08);
      }
    }

    .preloader-phoenix {
      animation: preloaderFly 2s ease-in-out infinite;
    }
  </style>

  <canvas id="starfield" aria-hidden="true"></canvas>
  <div class="space-dust" aria-hidden="true"></div>

  <!-- Custom Cursor Elements -->
  <div class="custom-cursor" id="customCursor"></div>
  <div class="custom-cursor-follower" id="customCursorFollower"></div>

  <!-- Scroll Progress Indicator -->
  <div class="scroll-progress" id="scrollProgress"></div>

  <!-- Scroll to Top Button -->
  <button id="scrollToTop" class="scroll-to-top" aria-label="Scroll to top of page" title="Back to top">
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="m18 15-6-6-6 6"/>
    </svg>
  </button>

  <header role="banner">
    <nav class="nav" role="navigation" aria-label="Main navigation">
      <div class="brand">
        <div class="brand-logo" aria-hidden="true">
          <img src="assets/img/phoenix.svg" alt="Phoenix Logo" style="width: 24px; height: 24px; filter: drop-shadow(0 0 8px rgba(255, 213, 107, 0.6));">
        </div>
        <span>Apoorv Deep Sahu</span>
      </div>
      <div class="menu">
        <a href="#hero" aria-label="Go to home section">Home</a>
        <a href="#about" aria-label="Go to about section">About</a>
        <a href="#services" aria-label="Go to services section">Services</a>
        <a href="#projects" aria-label="Go to projects section">Projects</a>
        <a href="#testimonials" aria-label="Go to testimonials section">Reviews</a>
        <a href="#contact" aria-label="Go to contact section">Contact</a>
      </div>
    </nav>
  </header>

  <main id="main-content" role="main">
