<!-- PROJECTS -->
<section id="projects" class="loading">
  <div class="wrap">
    <div class="section-header">
      <h2>Featured Projects</h2>
      <p class="muted">Real-world solutions demonstrating backend development expertise and business impact</p>
    </div>

    <div class="projects-grid" style="margin-top:2rem;">
      <article class="project-card enhanced-hover parallax-element" data-project="foundation">
        <div class="shine" aria-hidden="true"></div>
        <div class="project-header">
          <div class="project-icon">🏛️</div>
          <div class="project-meta">
            <h3>Online Foundation System</h3>
            <span class="project-role">Full-Stack Developer</span>
          </div>
        </div>

        <div class="project-content">
          <div class="project-section">
            <h4>Problem</h4>
            <p>UnnyanPathFoundation needed a digital platform to manage donations, track events, and engage with supporters efficiently, replacing manual processes.</p>
          </div>

          <div class="project-section">
            <h4>Solution</h4>
            <p>Developed a comprehensive web application with user registration, secure donation processing, event management, and an administrative dashboard for real-time monitoring.</p>
          </div>

          <div class="project-section">
            <h4>Results</h4>
            <p>Streamlined foundation operations, improved donor engagement, and provided transparent tracking of donations and activities.</p>
          </div>
        </div>

        <div class="tech-stack">
          <span class="tech-badge primary">Django</span>
          <span class="tech-badge secondary">MySQL</span>
          <span class="tech-badge">JavaScript</span>
          <span class="tech-badge">Bootstrap</span>
          <span class="tech-badge">HTML/CSS</span>
        </div>

        <div class="project-actions">
          <a class="btn secondary" href="https://github.com/apoorv-deep" target="_blank">
            <span class="btn-icon">📂</span>
            GitHub
          </a>
          <a class="btn tertiary" href="#contact">
            <span class="btn-icon">💬</span>
            Discuss Similar Project
          </a>
        </div>
      </article>

      <article class="project-card enhanced-hover parallax-element" data-project="payroll">
        <div class="shine" aria-hidden="true"></div>
        <div class="project-header">
          <div class="project-icon">💼</div>
          <div class="project-meta">
            <h3>Payroll Management System</h3>
            <span class="project-role">Backend Developer</span>
          </div>
        </div>

        <div class="project-content">
          <div class="project-section">
            <h4>Problem</h4>
            <p>Manual payroll processing was time-consuming and error-prone, requiring automated salary calculations and attendance tracking.</p>
          </div>

          <div class="project-section">
            <h4>Solution</h4>
            <p>Built a comprehensive system with automated payroll calculations, attendance management, employee data handling, and detailed reporting with Ajax-powered real-time updates.</p>
          </div>

          <div class="project-section">
            <h4>Results</h4>
            <p>Reduced payroll processing time by 80%, eliminated calculation errors, and provided comprehensive reporting for HR management.</p>
          </div>
        </div>

        <div class="tech-stack">
          <span class="tech-badge primary">Django</span>
          <span class="tech-badge secondary">MySQL</span>
          <span class="tech-badge">Ajax</span>
          <span class="tech-badge">JavaScript</span>
          <span class="tech-badge">Bootstrap</span>
        </div>

        <div class="project-actions">
          <a class="btn secondary" href="https://github.com/apoorv-deep" target="_blank">
            <span class="btn-icon">📂</span>
            GitHub
          </a>
          <a class="btn tertiary" href="#contact">
            <span class="btn-icon">💬</span>
            Discuss Similar Project
          </a>
        </div>
      </article>

      <article class="project-card enhanced-hover parallax-element" data-project="portfolio">
        <div class="shine" aria-hidden="true"></div>
        <div class="project-header">
          <div class="project-icon">🌟</div>
          <div class="project-meta">
            <h3>Space Phoenix Portfolio</h3>
            <span class="project-role">Full-Stack Developer</span>
          </div>
        </div>

        <div class="project-content">
          <div class="project-section">
            <h4>Problem</h4>
            <p>Needed a professional portfolio that showcases both technical skills and appeals to potential freelance clients with modern design and performance.</p>
          </div>

          <div class="project-section">
            <h4>Solution</h4>
            <p>Developed a modular PHP-based portfolio with space-themed design, smooth animations, contact form integration, and optimized performance for all devices.</p>
          </div>

          <div class="project-section">
            <h4>Results</h4>
            <p>Created a dual-purpose platform serving both as a developer portfolio and freelancer profile, with enhanced user engagement and professional presentation.</p>
          </div>
        </div>

        <div class="tech-stack">
          <span class="tech-badge primary">PHP</span>
          <span class="tech-badge secondary">JavaScript</span>
          <span class="tech-badge">GSAP</span>
          <span class="tech-badge">CSS3</span>
          <span class="tech-badge">Responsive</span>
        </div>

        <div class="project-actions">
          <a class="btn secondary" href="https://github.com/apoorv-deep" target="_blank">
            <span class="btn-icon">📂</span>
            GitHub
          </a>
          <a class="btn primary" href="#contact">
            <span class="btn-icon">🚀</span>
            Hire Me for Similar Work
          </a>
        </div>
      </article>
    </div>
  </div>
</section>
