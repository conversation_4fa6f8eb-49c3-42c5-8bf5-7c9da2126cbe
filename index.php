<?php
/**
 * Space Phoenix Portfolio - Modular PHP Version
 *
 * This is the main entry point that assembles all portfolio components
 * using PHP includes for better organization and maintainability.
 */

// Define access constant for security
define('PORTFOLIO_ACCESS', true);

// Include configuration
require_once 'config/config.php';

// Start session for CSRF protection
session_start();

// Include the header component (DOCTYPE, head, navigation)
include 'includes/header.php';
?>

    <!-- Include all portfolio sections -->
    <?php include 'sections/hero.php'; ?>

    <?php include 'sections/about.php'; ?>

    <?php include 'sections/tech-stack.php'; ?>

    <?php include 'sections/services.php'; ?>

    <?php include 'sections/projects.php'; ?>

    <?php include 'sections/testimonials.php'; ?>

    <?php include 'sections/contact.php'; ?>

<?php
// Include the footer component (footer, scripts, closing tags)
include 'includes/footer.php';
?>
