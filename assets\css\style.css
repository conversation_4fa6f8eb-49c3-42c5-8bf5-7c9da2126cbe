:root {
  --bg:#07070a;           /* deep space */
  --panel:#0c0f16;        /* panels */
  --text:#eaeaf2;         /* base text */
  --muted:#9aa0a6;        /* muted text */
  --flame:#ff6b35;        /* phoenix flame */
  --glow:#ffd56b;         /* soft glow */
  --accent:#6c63ff;       /* cosmic purple */
  --grid: 1200px;
}

/* Performance optimizations */
* {
  box-sizing: border-box;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  #starfield, .space-dust {
    display: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --bg: #000000;
    --panel: #1a1a1a;
    --text: #ffffff;
    --muted: #cccccc;
    --flame: #ff8800;
    --glow: #ffff00;
    --accent: #8888ff;
  }
}

/* Loading states */
.loading {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* Focus styles for accessibility */
.btn:focus, input:focus, textarea:focus, [tabindex]:focus {
  outline: 2px solid var(--glow);
  outline-offset: 2px;
}

/* Form styles */
form {
  max-width: 100%;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text);
}

input, textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(255,255,255,.12);
  border-radius: 8px;
  background: rgba(255,255,255,.03);
  color: var(--text);
  font-family: inherit;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

input:focus, textarea:focus {
  border-color: var(--glow);
  background: rgba(255,255,255,.06);
  box-shadow: 0 0 0 3px rgba(255,213,107,.1);
}

input.error, textarea.error {
  border-color: var(--flame);
  background: rgba(255,107,53,.05);
}

.field-error {
  color: var(--flame);
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

.form-message {
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.95rem;
  margin-bottom: 1rem;
}

.form-message.success {
  background: rgba(39,201,63,.1);
  border: 1px solid rgba(39,201,63,.3);
  color: #27c93f;
}

.form-message.error {
  background: rgba(255,107,53,.1);
  border: 1px solid rgba(255,107,53,.3);
  color: var(--flame);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-loading {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-loading::before {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,.3);
  border-top: 2px solid var(--glow);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* reCAPTCHA styling */
.g-recaptcha {
  transform: scale(0.95);
  transform-origin: 0 0;
  margin-bottom: 0.5rem;
}

/* Dark theme reCAPTCHA integration */
@media (prefers-color-scheme: dark) {
  .g-recaptcha {
    filter: invert(1) hue-rotate(180deg);
  }
}

/* reCAPTCHA container styling */
.g-recaptcha > div {
  border-radius: 8px !important;
  overflow: hidden;
}

/* Skip link for screen readers */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--panel);
  color: var(--text);
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

html, body {
  height: 100%;
  background: radial-gradient(1200px 800px at 70% -10%, rgba(108,99,255,.15), transparent 60%),
              radial-gradient(800px 600px at 10% 10%, rgba(255,107,53,.12), transparent 60%),
              var(--bg);
  color: var(--text);
  margin: 0;
  font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Inter, "Helvetica Neue", Arial, "Apple Color Emoji", "Segoe UI Emoji";
  overflow-x: hidden;
}

a { color: var(--glow); text-decoration: none; }
a:hover { text-decoration: underline; }

/* Global layout */
header {
  position: fixed; inset-inline: 0; top: 0; z-index: 50;
  backdrop-filter: saturate(120%) blur(10px);
  background: linear-gradient(to bottom, rgba(7,7,10,.75), transparent);
  border-bottom: 1px solid rgba(255,255,255,.06);
}

.nav {
  max-width: var(--grid); margin: 0 auto; padding: .6rem 1rem; display:flex; align-items:center; justify-content:space-between;
}

.brand { display:flex; gap:.6rem; align-items:center; font-weight:700; letter-spacing:.3px; }
.dot { width: 10px; height: 10px; border-radius: 9999px; background: linear-gradient(180deg, var(--glow), var(--flame)); box-shadow: 0 0 12px var(--glow); }

/* Brand Logo Styles */
.brand-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.brand-logo:hover {
  transform: scale(1.1) rotate(5deg);
}

.brand-logo img {
  animation: brandGlow 3s ease-in-out infinite;
}

@keyframes brandGlow {
  0%, 100% {
    filter: drop-shadow(0 0 8px rgba(255, 213, 107, 0.6));
  }
  50% {
    filter: drop-shadow(0 0 12px rgba(255, 213, 107, 0.8));
  }
}
.menu { display:flex; gap:1rem; font-size:.95rem; }
.menu a { opacity:.9 }

main { position: relative; }

/* Starfield canvas */
#starfield { position: fixed; inset:0; z-index:-2; display:block; }

/* Subtle dust overlay for depth */
.space-dust {
  position: fixed; inset:0; pointer-events:none; z-index:-1;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><filter id="f"><feTurbulence baseFrequency="0.9" numOctaves="2" type="fractalNoise"/></filter><rect width="200" height="200" opacity="0.03" filter="url(%23f)"/></svg>') repeat;
  mix-blend-mode: screen;
}

/* Sections */
section { padding: 7rem 1rem; }
.wrap { max-width: var(--grid); margin: 0 auto; }

/* HERO */
.hero { display:grid; min-height: 100dvh; place-items:center; position: relative; }
.hero-inner { display:grid; grid-template-columns: 1.2fr 1fr; gap: 3rem; align-items:center; width: 100%; }
.hero p.kicker { color: var(--muted); text-transform: uppercase; font-size: .8rem; letter-spacing: .2em; margin: 0 0 .6rem; }
.title { font-size: clamp(2.2rem, 5vw, 4rem); line-height: 1.05; margin: 0 0 1rem; font-weight: 800; }
.subtitle { color: var(--muted); font-size: clamp(1rem, 2vw, 1.2rem); max-width: 60ch; }

/* Highlight spans in subtitle */
.subtitle .highlight {
  color: var(--glow);
  font-weight: 600;
  background: linear-gradient(135deg, var(--glow), var(--accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.cta-group { margin-top: 1.5rem; display:flex; gap:.8rem; flex-wrap: wrap; }
.cta { margin-top: 1.5rem; display:flex; gap:.8rem; flex-wrap: wrap; }
.btn { border:1px solid rgba(255,255,255,.12); background: rgba(255,255,255,.03); color: var(--text); padding:.8rem 1rem; border-radius: 16px; font-weight:600; cursor:pointer; display: inline-flex; align-items: center; gap: 0.5rem; text-decoration: none; transition: all 0.3s ease; }
.btn.primary { background: linear-gradient(180deg, rgba(255,213,107,.22), rgba(255,107,53,.22)); border-color: rgba(255,213,107,.45); box-shadow: 0 6px 30px rgba(255,213,107,.18); }
.btn.secondary { background: linear-gradient(180deg, rgba(108,99,255,.22), rgba(108,99,255,.12)); border-color: rgba(108,99,255,.45); box-shadow: 0 6px 30px rgba(108,99,255,.15); }
.btn.tertiary { background: rgba(255,255,255,.08); border-color: rgba(255,255,255,.25); }
.btn:hover { transform: translateY(-2px); }

/* Button icons */
.btn-icon {
  font-size: 1rem;
  opacity: 0.9;
}

.phoenix-wrap { position: relative; }
.phoenix-glow {
  position: absolute;
  inset: -15%;
  filter: blur(35px);
  opacity: .4;
  background: radial-gradient(closest-side, rgba(108, 99, 255, 0.6), rgba(255, 213, 107, 0.4) 40%, transparent 70%);
  border-radius: 50%;
}
img#phoenix {
  width: min(480px, 38vw);
  height: auto;
  display: block;
  transform-origin: 50% 50%;
  border-radius: 16px;
  filter: drop-shadow(0 0 15px rgba(108, 99, 255, 0.25)) drop-shadow(0 0 8px rgba(255, 213, 107, 0.2));
  transition: all 0.4s ease, opacity 0.6s ease;
  animation: phoenixFly 10s ease-in-out infinite, phoenixGlow 6s ease-in-out infinite;
  /* Ensure image maintains aspect ratio and looks professional */
  object-fit: cover;
  background: transparent;
  border: none;
  box-shadow: none;
  /* Professional loading state */
  opacity: 0;
  animation-delay: 0.3s;
}

/* Fade in when image loads */
img#phoenix.loaded {
  opacity: 1;
}

/* Subtle Profile Floating Animation */
@keyframes phoenixFly {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
  }
  12.5% {
    transform: translateY(-3px) translateX(2px) rotate(0.5deg) scale(1.005);
  }
  25% {
    transform: translateY(-6px) translateX(4px) rotate(0.8deg) scale(1.008);
  }
  37.5% {
    transform: translateY(-5px) translateX(6px) rotate(0deg) scale(1.005);
  }
  50% {
    transform: translateY(-4px) translateX(8px) rotate(-0.3deg) scale(1);
  }
  62.5% {
    transform: translateY(-7px) translateX(6px) rotate(0.4deg) scale(1.005);
  }
  75% {
    transform: translateY(-8px) translateX(4px) rotate(1deg) scale(1.008);
  }
  87.5% {
    transform: translateY(-4px) translateX(2px) rotate(0.3deg) scale(1.005);
  }
}

/* Professional profile glow animation */
@keyframes phoenixGlow {
  0%, 100% {
    filter: drop-shadow(0 0 15px rgba(108, 99, 255, 0.25)) drop-shadow(0 0 8px rgba(255, 213, 107, 0.2));
  }
  25% {
    filter: drop-shadow(0 0 18px rgba(108, 99, 255, 0.3)) drop-shadow(0 0 10px rgba(255, 213, 107, 0.25));
  }
  50% {
    filter: drop-shadow(0 0 22px rgba(108, 99, 255, 0.35)) drop-shadow(0 0 12px rgba(255, 213, 107, 0.3));
  }
  75% {
    filter: drop-shadow(0 0 18px rgba(108, 99, 255, 0.3)) drop-shadow(0 0 10px rgba(255, 213, 107, 0.25));
  }
}

/* Subtle brightness enhancement for profile */
@keyframes wingFlap {
  0%, 100% {
    filter: drop-shadow(0 0 15px rgba(108, 99, 255, 0.25)) drop-shadow(0 0 8px rgba(255, 213, 107, 0.2)) brightness(1);
  }
  50% {
    filter: drop-shadow(0 0 20px rgba(108, 99, 255, 0.35)) drop-shadow(0 0 12px rgba(255, 213, 107, 0.3)) brightness(1.05);
  }
}

/* Professional hover and focus animations for profile */
img#phoenix:hover,
img#phoenix:focus {
  animation: phoenixFlyHover 6s ease-in-out infinite, wingFlap 3s ease-in-out infinite;
  outline: 2px solid rgba(108, 99, 255, 0.4);
  outline-offset: 4px;
}

img#phoenix:focus {
  outline: 2px solid rgba(108, 99, 255, 0.6);
}

@keyframes phoenixFlyHover {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
  }
  25% {
    transform: translateY(-8px) translateX(6px) rotate(1.5deg) scale(1.02);
  }
  50% {
    transform: translateY(-5px) translateX(12px) rotate(-0.8deg) scale(1.015);
  }
  75% {
    transform: translateY(-10px) translateX(6px) rotate(2deg) scale(1.02);
  }
}

/* ABOUT */
.panel { background: linear-gradient(180deg, rgba(255,255,255,.02), rgba(255,255,255,.00)); border:1px solid rgba(255,255,255,.06); border-radius: 20px; padding: 1.2rem; }
.about { display:grid; gap: 1.2rem; grid-template-columns: 1.2fr .8fr; align-items: stretch; }
.terminal { background: #0a0d13; border-radius: 14px; border:1px solid rgba(255,255,255,.06); position: relative; overflow:hidden; }
.terminal .bar { display:flex; gap:.5rem; align-items:center; padding:.6rem .8rem; border-bottom:1px solid rgba(255,255,255,.06); background: linear-gradient(180deg, rgba(255,255,255,.04), transparent); }
.status-dot { width:10px; height:10px; border-radius:9999px; background:#ff5f56; box-shadow: 0 0 8px rgba(255,95,86,.8); }
.status-dot.yellow { background:#ffbd2e; box-shadow:0 0 8px rgba(255,189,46,.8); }
.status-dot.green { background:#27c93f; box-shadow:0 0 8px rgba(39,201,63,.8); }
.terminal pre { margin: 0; padding: 1rem; font-family: ui-monospace, SFMono-Regular, Menlo, Consolas, "Liberation Mono", monospace; color: #c8e1ff; font-size: .95rem; min-height: 220px; white-space: pre-wrap; }

/* Enhanced About Section Styles */
.intro-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text);
  margin-bottom: 2rem;
}

.value-proposition {
  margin: 2rem 0;
}

.value-proposition h3 {
  color: var(--glow);
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.value-points {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.value-point {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255,255,255,.03);
  border-radius: 12px;
  border: 1px solid rgba(255,255,255,.06);
}

.value-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.value-point strong {
  color: var(--text);
  display: block;
  margin-bottom: 0.3rem;
}

.value-point p {
  color: var(--muted);
  margin: 0;
  font-size: 0.9rem;
}

.skills-section {
  margin: 2rem 0;
}

.skills-section h3 {
  color: var(--glow);
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
}

.skill-categories {
  display: grid;
  gap: 1.5rem;
}

.skill-category h4 {
  color: var(--text);
  margin-bottom: 0.8rem;
  font-size: 1rem;
  font-weight: 600;
}

.skill-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.badge.primary { background: rgba(255,213,107,.2); color: var(--glow); border: 1px solid rgba(255,213,107,.3); }
.badge.secondary { background: rgba(108,99,255,.2); color: var(--accent); border: 1px solid rgba(108,99,255,.3); }
.badge.tertiary { background: rgba(255,107,53,.2); color: var(--flame); border: 1px solid rgba(255,107,53,.3); }

.credentials {
  margin-top: 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.credential-section h4 {
  color: var(--text);
  margin-bottom: 0.8rem;
  font-size: 1rem;
  font-weight: 600;
}

.credential-section p {
  color: var(--muted);
  font-size: 0.9rem;
  margin: 0.3rem 0;
}

/* TECH STACK VISUALIZATION */
.tech-universe {
  position: relative;
  width: 100%;
  max-width: 600px;
  aspect-ratio: 1;
  margin: 3rem auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.phoenix-core {
  position: absolute;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--flame), var(--glow));
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  animation: phoenixPulse 4s ease-in-out infinite;
}

.core-glow {
  position: absolute;
  inset: -20px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255,107,53,0.3), rgba(255,213,107,0.2), transparent);
  filter: blur(15px);
  animation: coreGlow 3s ease-in-out infinite alternate;
}

.core-symbol {
  font-size: 2rem;
  z-index: 2;
}

.core-label {
  font-size: 0.8rem;
  font-weight: 600;
  text-align: center;
  color: var(--bg);
  z-index: 2;
  line-height: 1.1;
}

.orbit {
  position: absolute;
  border: 1px solid rgba(255,255,255,0.1);
  border-radius: 50%;
  animation-duration: 60s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

.orbit-primary {
  width: 300px;
  height: 300px;
  animation-name: orbitRotate;
}

.orbit-secondary {
  width: 450px;
  height: 450px;
  animation-name: orbitRotateReverse;
  animation-duration: 80s;
}

.orbit-tertiary {
  width: 580px;
  height: 580px;
  animation-name: orbitRotate;
  animation-duration: 100s;
}

.tech-planet {
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--panel);
  border: 2px solid rgba(255,255,255,0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transform-origin: center;
  animation: planetFloat 3s ease-in-out infinite;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tech-planet:hover {
  transform: scale(1.1);
  border-color: var(--glow);
  box-shadow: 0 0 20px rgba(255,213,107,0.4);
}

.planet-icon {
  font-size: 1.5rem;
  margin-bottom: 0.2rem;
}

.planet-label {
  font-size: 0.7rem;
  font-weight: 600;
  color: var(--text);
  text-align: center;
}

.planet-glow {
  position: absolute;
  inset: -10px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(108,99,255,0.2), transparent);
  filter: blur(8px);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tech-planet:hover .planet-glow,
.tech-planet.highlighted .planet-glow {
  opacity: 1;
}

.tech-planet.highlighted {
  transform: scale(1.15);
  border-color: var(--glow);
  box-shadow: 0 0 25px rgba(255,213,107,0.6);
  z-index: 10;
}

.asteroid {
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255,255,255,0.05);
  border: 1px solid rgba(255,255,255,0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: asteroidFloat 4s ease-in-out infinite;
  left: var(--x);
  top: var(--y);
}

.asteroid-label {
  font-size: 0.6rem;
  color: var(--muted);
  text-align: center;
  font-weight: 500;
}

.tech-details {
  margin-top: 4rem;
}

.tech-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.category-card {
  background: var(--panel);
  border: 1px solid rgba(255,255,255,.08);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.category-card:hover {
  transform: translateY(-5px);
  border-color: rgba(255,213,107,.3);
  box-shadow: 0 10px 30px rgba(255,213,107,.1);
}

.category-card h3 {
  color: var(--glow);
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.category-card p {
  color: var(--muted);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.category-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
}

.skill-tag {
  padding: 0.4rem 0.8rem;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 600;
}

.skill-tag.primary {
  background: rgba(255,213,107,.2);
  color: var(--glow);
  border: 1px solid rgba(255,213,107,.3);
}

.skill-tag.secondary {
  background: rgba(108,99,255,.2);
  color: var(--accent);
  border: 1px solid rgba(108,99,255,.3);
}

.skill-tag.tertiary {
  background: rgba(255,107,53,.2);
  color: var(--flame);
  border: 1px solid rgba(255,107,53,.3);
}

/* SERVICES */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.service-card {
  background: var(--panel);
  border: 1px solid rgba(255,255,255,.08);
  border-radius: 16px;
  padding: 2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--glow), var(--accent));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.service-card:hover::before {
  opacity: 1;
}

.service-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.service-card h3 {
  color: var(--text);
  margin-bottom: 1rem;
  font-size: 1.3rem;
  font-weight: 700;
}

.service-card p {
  color: var(--muted);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.service-features {
  list-style: none;
  padding: 0;
  margin: 1.5rem 0;
}

.service-features li {
  color: var(--text);
  padding: 0.4rem 0;
  position: relative;
  padding-left: 1.5rem;
}

.service-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--glow);
  font-weight: bold;
}

.service-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

.tech-tag {
  background: rgba(108,99,255,.15);
  color: var(--accent);
  padding: 0.3rem 0.6rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
}

.cta-card {
  background: linear-gradient(135deg, rgba(255,213,107,.1), rgba(108,99,255,.1));
  border-color: rgba(255,213,107,.3);
  text-align: center;
}

.cta-content {
  margin-top: 1rem;
}

.cta-text {
  color: var(--glow);
  font-weight: 600;
  margin-bottom: 1rem;
}

.services-footer {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255,255,255,.08);
}

.process-overview h3 {
  text-align: center;
  color: var(--glow);
  margin-bottom: 2rem;
  font-size: 1.5rem;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.process-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  text-align: left;
}

.step-number {
  background: linear-gradient(135deg, var(--glow), var(--accent));
  color: var(--bg);
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content h4 {
  color: var(--text);
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.step-content p {
  color: var(--muted);
  font-size: 0.9rem;
  margin: 0;
}

/* ENHANCED PROJECTS */
.projects-grid { display:grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 2rem; perspective: 1000px; }

.project-card {
  background: var(--panel);
  border: 1px solid rgba(255,255,255,.08);
  border-radius: 16px;
  padding: 2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.project-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.project-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.project-meta h3 {
  color: var(--text);
  margin: 0 0 0.5rem 0;
  font-size: 1.4rem;
  font-weight: 700;
}

.project-role {
  color: var(--glow);
  font-size: 0.9rem;
  font-weight: 600;
  background: rgba(255,213,107,.1);
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  border: 1px solid rgba(255,213,107,.2);
}

.project-content {
  margin-bottom: 1.5rem;
}

.project-section {
  margin-bottom: 1rem;
}

.project-section h4 {
  color: var(--accent);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.project-section h4::before {
  content: '';
  width: 4px;
  height: 4px;
  background: var(--accent);
  border-radius: 50%;
}

.project-section p {
  color: var(--muted);
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
}

.project-actions {
  display: flex;
  gap: 0.8rem;
  flex-wrap: wrap;
  margin-top: 1.5rem;
}

/* TESTIMONIALS */
.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.testimonial-card {
  background: var(--panel);
  border: 1px solid rgba(255,255,255,.08);
  border-radius: 16px;
  padding: 2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.testimonial-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--glow), var(--accent));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.testimonial-card:hover::before {
  opacity: 1;
}

.testimonial-content {
  margin-bottom: 1.5rem;
  position: relative;
}

.quote-icon {
  font-size: 3rem;
  color: var(--glow);
  opacity: 0.3;
  position: absolute;
  top: -10px;
  left: -5px;
  font-family: serif;
}

.testimonial-content blockquote {
  color: var(--text);
  font-style: italic;
  line-height: 1.6;
  margin: 0;
  padding-left: 2rem;
  font-size: 1rem;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--glow), var(--accent));
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-initial {
  color: var(--bg);
  font-weight: bold;
  font-size: 1.2rem;
}

.author-info h4 {
  color: var(--text);
  margin: 0 0 0.2rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.author-info p {
  color: var(--muted);
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
}

.testimonial-rating {
  display: flex;
  gap: 0.1rem;
}

.star {
  color: var(--glow);
  font-size: 0.9rem;
}

.testimonial-project {
  display: flex;
  justify-content: flex-end;
}

.project-tag {
  background: rgba(108,99,255,.15);
  color: var(--accent);
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.testimonials-footer {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255,255,255,.08);
}

.testimonials-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
  text-align: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--glow);
  background: linear-gradient(135deg, var(--glow), var(--accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  color: var(--muted);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.testimonials-cta {
  text-align: center;
  padding: 2rem;
  background: rgba(255,255,255,.02);
  border-radius: 16px;
  border: 1px solid rgba(255,255,255,.06);
}

.testimonials-cta h3 {
  color: var(--glow);
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.testimonials-cta p {
  color: var(--muted);
  margin-bottom: 1.5rem;
}
.card {
  position:relative; overflow:hidden; border-radius: 20px; background: var(--panel);
  border:1px solid rgba(255,255,255,.08); padding:1.5rem; min-height: 280px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-style: preserve-3d;
  cursor: pointer;
}
.card:hover {
  transform: translateY(-8px) rotateX(5deg) rotateY(5deg);
  box-shadow: 0 20px 60px rgba(0,0,0,.4), 0 0 0 1px rgba(255,213,107,.2);
  border-color: rgba(255,213,107,.3);
}
.card h3 { margin:.4rem 0 .6rem; font-size: 1.3rem; }
.card p { color: var(--muted); line-height: 1.6; }
.card .shine {
  position:absolute; inset:-30%;
  background: conic-gradient(from 0deg, rgba(255,213,107,.12), transparent 25%, transparent 75%, rgba(255,213,107,.12));
  transform: rotate(0deg); opacity:.6; pointer-events:none;
  transition: opacity 0.3s ease;
}
.card:hover .shine { opacity: 1; }

.card .tech-stack {
  display: flex; flex-wrap: wrap; gap: 0.4rem; margin-top: 1rem;
}
.card .tech-badge {
  background: rgba(255,255,255,.06); border: 1px solid rgba(255,255,255,.1);
  padding: 0.3rem 0.6rem; border-radius: 12px; font-size: 0.8rem;
  color: var(--glow); transition: all 0.3s ease;
}
.card:hover .tech-badge {
  background: rgba(255,213,107,.1); border-color: rgba(255,213,107,.3);
  transform: translateY(-2px);
}

.card .project-icon {
  position: absolute; top: 1rem; right: 1rem;
  width: 40px; height: 40px; border-radius: 50%;
  background: linear-gradient(135deg, var(--glow), var(--flame));
  display: flex; align-items: center; justify-content: center;
  font-size: 1.2rem; opacity: 0.8;
  transition: all 0.3s ease;
}
.card:hover .project-icon {
  transform: rotate(360deg) scale(1.1);
  opacity: 1;
}

/* SKILLS */
.skills { display:grid; grid-template-columns: .9fr 1.1fr; gap: 2rem; align-items:center; }
.skill-cloud { position: relative; aspect-ratio: 1/1; }
.skill-cloud svg { width: 100%; height: auto; display:block; }
.badge { display:inline-flex; gap:.5rem; align-items:center; background: rgba(255,255,255,.04); border:1px solid rgba(255,255,255,.08); padding:.5rem .7rem; border-radius: 9999px; margin: .25rem; font-size: .95rem; }

/* CONTACT */
.contact-methods {
  margin: 2rem 0;
}

.contact-methods h4,
.professional-links h4 {
  color: var(--text);
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.quick-contact-grid,
.profile-links-grid {
  display: grid;
  gap: 0.8rem;
}

.quick-contact-grid {
  grid-template-columns: 1fr 1fr;
}

.profile-links-grid {
  grid-template-columns: 1fr 1fr;
}

.contact-method,
.profile-link {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem;
  background: rgba(255,255,255,.03);
  border: 1px solid rgba(255,255,255,.08);
  border-radius: 12px;
  text-decoration: none;
  color: var(--text);
  transition: all 0.3s ease;
}

.contact-method:hover,
.profile-link:hover {
  transform: translateY(-2px);
  border-color: var(--glow);
  box-shadow: 0 5px 20px rgba(255,213,107,.15);
}

.contact-method.whatsapp:hover {
  border-color: #25D366;
  box-shadow: 0 5px 20px rgba(37,211,102,.15);
}

.contact-method.telegram:hover {
  border-color: #0088cc;
  box-shadow: 0 5px 20px rgba(0,136,204,.15);
}

.profile-link.github:hover {
  border-color: #333;
  box-shadow: 0 5px 20px rgba(51,51,51,.15);
}

.profile-link.linkedin:hover {
  border-color: #0077b5;
  box-shadow: 0 5px 20px rgba(0,119,181,.15);
}

.profile-link.upwork:hover {
  border-color: #14a800;
  box-shadow: 0 5px 20px rgba(20,168,0,.15);
}

.profile-link.fiverr:hover {
  border-color: #1dbf73;
  box-shadow: 0 5px 20px rgba(29,191,115,.15);
}

.contact-icon,
.profile-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.contact-info,
.profile-info {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.contact-info strong,
.profile-info strong {
  color: var(--text);
  font-size: 0.95rem;
}

.contact-info span,
.profile-info span {
  color: var(--muted);
  font-size: 0.8rem;
}

.professional-links {
  margin: 2rem 0;
}

form { display:grid; gap: .9rem; }
label { font-size:.9rem; color: var(--muted); }
input, textarea { width:100%; background: rgba(255,255,255,.03); border:1px solid rgba(255,255,255,.1); color: var(--text); border-radius: 12px; padding:.8rem; outline: none; }
input:focus, textarea:focus { border-color: var(--glow); box-shadow: 0 0 0 3px rgba(255,213,107,.15); }
textarea { min-height: 140px; }

/* FOOTER */
footer {
  background: linear-gradient(180deg, transparent, rgba(0,0,0,0.3));
  border-top: 1px solid rgba(255,255,255,.08);
  padding: 3rem 1rem 2rem;
  margin-top: 4rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-brand {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.phoenix-brand {
  width: 32px;
  height: 32px;
  filter: drop-shadow(0 0 8px rgba(255, 213, 107, 0.6));
  animation: brandGlow 3s ease-in-out infinite;
}

.brand-text {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--glow);
  background: linear-gradient(135deg, var(--glow), var(--accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-tagline {
  color: var(--muted);
  font-style: italic;
  margin: 0;
}

.footer-nav h4,
.footer-contact h4,
.footer-social h4 {
  color: var(--text);
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-links a {
  color: var(--muted);
  text-decoration: none;
  transition: color 0.3s ease;
  padding: 0.3rem 0;
}

.footer-links a:hover {
  color: var(--glow);
}

.footer-contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.footer-contact-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--muted);
  text-decoration: none;
  transition: color 0.3s ease;
  padding: 0.3rem 0;
}

.footer-contact-link:hover {
  color: var(--glow);
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255,255,255,.05);
  border: 1px solid rgba(255,255,255,.1);
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-link:hover {
  transform: translateY(-2px);
  border-color: var(--glow);
  box-shadow: 0 5px 15px rgba(255,213,107,.2);
}

.social-icon {
  font-size: 1.2rem;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255,255,255,.08);
  flex-wrap: wrap;
  gap: 1rem;
}

.footer-copyright p {
  color: var(--muted);
  margin: 0;
  font-size: 0.9rem;
}

.footer-tech {
  font-size: 0.8rem;
  opacity: 0.7;
}

.footer-cta {
  flex-shrink: 0;
}

/* Utils */
.muted { color: var(--muted); }
.grid-2 { display:grid; grid-template-columns: 1fr 1fr; gap: 1rem; }

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
  :root { --grid: 95%; }
  .projects-grid { grid-template-columns: repeat(2, 1fr); gap: 1.2rem; }
}

@media (max-width: 1000px) {
  .hero-inner { grid-template-columns: 1fr; text-align:center; gap: 2rem; }
  .phoenix-wrap { order:-1; }
  .about { grid-template-columns: 1fr; }
  .credentials { grid-template-columns: 1fr; }
  .value-points { gap: 0.8rem; }
  .value-point { padding: 0.8rem; }
  .skills { grid-template-columns: 1fr; text-align: center; }
  .skill-cloud { max-width: 300px; margin: 0 auto; }

  /* Reduce motion on tablets */
  .card:hover { transform: translateY(-4px); }
}

@media (max-width: 768px) {
  section { padding: 4rem 1rem; }
  .title { font-size: clamp(1.8rem, 6vw, 2.5rem); }
  .projects-grid { grid-template-columns: 1fr; gap: 1rem; }
  .card { padding: 1.2rem; min-height: 240px; }
  .project-card { padding: 1.5rem; }
  .project-actions { flex-direction: column; }
  .project-actions .btn { width: 100%; justify-content: center; }
  .cta, .cta-group { flex-direction: column; align-items: center; }
  .btn { width: 100%; max-width: 280px; text-align: center; justify-content: center; }

  /* Services responsive */
  .services-grid { grid-template-columns: 1fr; gap: 1rem; }
  .service-card { padding: 1.5rem; }
  .process-steps { grid-template-columns: 1fr; gap: 1rem; }
  .process-step { flex-direction: column; text-align: center; align-items: center; }

  /* Testimonials responsive */
  .testimonials-grid { grid-template-columns: 1fr; gap: 1rem; }
  .testimonial-card { padding: 1.5rem; }
  .testimonials-stats { grid-template-columns: repeat(2, 1fr); gap: 1rem; }
  .stat-number { font-size: 2rem; }

  /* Tech Stack responsive */
  .tech-universe { max-width: 400px; }
  .orbit-primary { width: 200px; height: 200px; }
  .orbit-secondary { width: 300px; height: 300px; }
  .orbit-tertiary { width: 380px; height: 380px; }
  .tech-planet { width: 45px; height: 45px; }
  .planet-icon { font-size: 1.2rem; }
  .planet-label { font-size: 0.6rem; }
  .phoenix-core { width: 80px; height: 80px; }
  .core-symbol { font-size: 1.5rem; }
  .core-label { font-size: 0.7rem; }
  .tech-categories { grid-template-columns: 1fr; gap: 1rem; }
  .category-card { padding: 1.5rem; }

  /* Contact responsive */
  .quick-contact-grid,
  .profile-links-grid { grid-template-columns: 1fr; }
  .contact-method,
  .profile-link { padding: 0.8rem; }

  /* Footer responsive */
  .footer-content { grid-template-columns: 1fr 1fr; gap: 1.5rem; }
  .footer-brand { grid-column: 1 / -1; text-align: center; }
  .footer-bottom { flex-direction: column; text-align: center; }

  /* Mobile navigation improvements */
  .nav { padding: 0.8rem 1rem; }
  .menu { gap: 0.8rem; font-size: 0.9rem; }

  /* Touch-friendly sizing */
  .card .tech-badge { padding: 0.4rem 0.8rem; }
  input, textarea { padding: 1rem; font-size: 16px; } /* Prevent zoom on iOS */
}

@media (max-width: 480px) {
  .hero { min-height: 90vh; }
  .title { font-size: 2rem; line-height: 1.1; }
  .subtitle { font-size: 1rem; }
  section { padding: 3rem 0.8rem; }
  .card { padding: 1rem; min-height: 200px; }
  .nav { flex-direction: column; gap: 1rem; text-align: center; }
  .menu { justify-content: center; flex-wrap: wrap; }

  /* Footer mobile */
  .footer-content { grid-template-columns: 1fr; gap: 2rem; text-align: center; }
  .footer-brand { grid-column: 1; }
  .social-links { justify-content: center; }

  /* Adjust brand logo for mobile */
  .brand-logo img {
    width: 20px;
    height: 20px;
  }

  /* Simplified animations for mobile */
  .card:hover { transform: none; box-shadow: 0 8px 25px rgba(0,0,0,.3); }
  .phoenix-wrap { max-width: 280px; margin: 0 auto; }
  img#phoenix {
    width: min(280px, 90vw);
    animation: phoenixFlyMobile 8s ease-in-out infinite;
    border-radius: 12px;
    filter: drop-shadow(0 0 12px rgba(108, 99, 255, 0.2)) drop-shadow(0 0 6px rgba(255, 213, 107, 0.15));
    background: transparent;
    border: none;
    box-shadow: none;
  }

  .phoenix-glow {
    inset: -12%;
    filter: blur(30px);
    opacity: .3;
  }

  /* Professional mobile profile animation */
  @keyframes phoenixFlyMobile {
    0%, 100% {
      transform: translateY(0px) rotate(0deg) scale(1);
    }
    25% {
      transform: translateY(-4px) rotate(0.5deg) scale(1.002);
    }
    50% {
      transform: translateY(-2px) rotate(-0.3deg) scale(1.001);
    }
    75% {
      transform: translateY(-6px) rotate(0.8deg) scale(1.003);
    }
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .card:hover { transform: none; }
  .card:active { transform: scale(0.98); }
  .btn:hover { transform: none; }
  .btn:active { transform: scale(0.95); }

  /* Disable complex animations on touch devices */
  .card .shine { display: none; }

  /* Simplified phoenix animation for touch devices */
  img#phoenix {
    animation: phoenixFlyTouch 8s ease-in-out infinite;
  }

  @keyframes phoenixFlyTouch {
    0%, 100% {
      transform: translateY(0px) scale(1);
    }
    33% {
      transform: translateY(-3px) scale(1.001);
    }
    66% {
      transform: translateY(-6px) scale(1.002);
    }
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .dot { box-shadow: 0 0 8px var(--glow); }
  .status-dot { box-shadow: 0 0 6px rgba(255,95,86,.6); }
}

/* Landscape mobile orientation */
@media (max-height: 500px) and (orientation: landscape) {
  .hero { min-height: 100vh; padding: 2rem 1rem; }
  .hero-inner { gap: 1.5rem; }
  section { padding: 3rem 1rem; }
}

/* Screen reader only class */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Custom Cursor Effects */
.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: rgba(255, 213, 107, 0.8);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  transition: transform 0.1s ease;
  transform: translate(-50%, -50%);
}

.custom-cursor-follower {
  position: fixed;
  width: 40px;
  height: 40px;
  border: 2px solid rgba(255, 213, 107, 0.3);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9998;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.cursor-hover .custom-cursor {
  transform: translate(-50%, -50%) scale(1.5);
  background: rgba(255, 107, 53, 0.9);
}

.cursor-hover .custom-cursor-follower {
  transform: translate(-50%, -50%) scale(1.5);
  border-color: rgba(255, 107, 53, 0.5);
}

.cursor-text .custom-cursor {
  transform: translate(-50%, -50%) scale(0.5);
}

.cursor-text .custom-cursor-follower {
  transform: translate(-50%, -50%) scale(2);
  border-color: rgba(108, 99, 255, 0.5);
}

/* Hide default cursor on interactive elements */
body.custom-cursor-enabled {
  cursor: none;
}

body.custom-cursor-enabled a,
body.custom-cursor-enabled button,
body.custom-cursor-enabled .btn,
body.custom-cursor-enabled .card {
  cursor: none;
}

/* Scroll Progress Indicator */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(90deg, var(--glow), var(--flame), var(--accent));
  z-index: 1000;
  transition: width 0.1s ease;
}

/* Scroll to Top Button */
.scroll-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 3.5rem;
  height: 3.5rem;
  background: var(--panel);
  border: 2px solid var(--accent);
  border-radius: 50%;
  color: var(--glow);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px) scale(0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 999;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(255, 213, 107, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.scroll-to-top svg {
  width: 1.5rem;
  height: 1.5rem;
  transition: transform 0.2s ease;
}

.scroll-to-top:hover {
  background: rgba(255, 213, 107, 0.1);
  border-color: var(--glow);
  color: var(--flame);
  transform: translateY(0) scale(1.05);
  box-shadow:
    0 6px 25px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(255, 213, 107, 0.4);
}

.scroll-to-top:hover svg {
  transform: translateY(-2px);
}

.scroll-to-top:active {
  transform: translateY(0) scale(0.95);
}

.scroll-to-top:focus {
  outline: 2px solid var(--glow);
  outline-offset: 2px;
}

.scroll-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
  animation: scrollToTopAppear 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Subtle pulse effect when first appearing */
.scroll-to-top.visible.first-appear {
  animation: scrollToTopAppear 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards,
             subtlePulse 2s ease-in-out 0.4s;
}

@keyframes scrollToTopAppear {
  0% {
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px) scale(0.8) rotate(-180deg);
  }
  100% {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1) rotate(0deg);
  }
}

@keyframes subtlePulse {
  0%, 100% {
    box-shadow:
      0 4px 20px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(255, 213, 107, 0.2);
  }
  50% {
    box-shadow:
      0 4px 20px rgba(0, 0, 0, 0.3),
      0 0 30px rgba(255, 213, 107, 0.4);
  }
}

/* Mobile responsiveness for scroll to top button */
@media (max-width: 768px) {
  .scroll-to-top {
    bottom: 1.5rem;
    right: 1.5rem;
    width: 3rem;
    height: 3rem;
  }

  .scroll-to-top svg {
    width: 1.25rem;
    height: 1.25rem;
  }
}

@media (max-width: 480px) {
  .scroll-to-top {
    bottom: 1rem;
    right: 1rem;
    width: 2.75rem;
    height: 2.75rem;
  }

  .scroll-to-top svg {
    width: 1.125rem;
    height: 1.125rem;
  }
}

/* Enhanced Hover Effects */
.enhanced-hover {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.enhanced-hover::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 213, 107, 0.1) 0%, transparent 70%);
  transition: all 0.4s ease;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 1;
}

.enhanced-hover:hover::before {
  width: 200%;
  height: 200%;
}

.enhanced-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(255, 213, 107, 0.2);
}

/* Parallax Elements */
.parallax-element {
  transition: transform 0.1s ease-out;
}

.parallax-slow {
  transition: transform 0.2s ease-out;
}

.parallax-fast {
  transition: transform 0.05s ease-out;
}

/* Mobile and Touch Device Optimizations */
@media (max-width: 768px) {
  .custom-cursor,
  .custom-cursor-follower {
    display: none !important;
  }

  body.custom-cursor-enabled {
    cursor: auto;
  }

  body.custom-cursor-enabled a,
  body.custom-cursor-enabled button,
  body.custom-cursor-enabled .btn,
  body.custom-cursor-enabled .card {
    cursor: pointer;
  }

  .parallax-element {
    transform: none !important;
  }

  .enhanced-hover:hover {
    transform: none;
  }

  .enhanced-hover:active {
    transform: scale(0.98);
  }
}

/* Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {
  .custom-cursor,
  .custom-cursor-follower,
  .scroll-progress {
    display: none !important;
  }

  .scroll-to-top {
    transition: opacity 0.2s ease !important;
    animation: none !important;
  }

  .scroll-to-top:hover {
    transform: none !important;
  }

  .scroll-to-top svg {
    transform: none !important;
  }

  .parallax-element,
  .enhanced-hover,
  #phoenix,
  .phoenix-wrap {
    transform: none !important;
    transition: none !important;
  }

  .enhanced-hover::before {
    display: none;
  }
}

/* Performance Optimizations */
.parallax-element,
.enhanced-hover,
#phoenix,
.phoenix-wrap {
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Smooth Scroll Enhancement */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}

/* Loading Animation for Enhanced Elements */
.enhanced-hover,
.parallax-element {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tech Stack Animation Keyframes */
@keyframes phoenixPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(255,107,53,0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(255,213,107,0.5);
  }
}

@keyframes coreGlow {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

@keyframes orbitRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes orbitRotateReverse {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

@keyframes planetFloat {
  0%, 100% {
    transform: translateY(0px) rotate(calc(-1 * var(--angle)));
  }
  50% {
    transform: translateY(-5px) rotate(calc(-1 * var(--angle)));
  }
}

@keyframes asteroidFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-3px) rotate(120deg);
  }
  66% {
    transform: translateY(2px) rotate(240deg);
  }
}

@media (prefers-reduced-motion: reduce) {
  .enhanced-hover,
  .parallax-element {
    animation: none;
    opacity: 1;
    transform: none;
  }

  /* Disable phoenix flying animations for reduced motion */
  img#phoenix {
    animation: none !important;
  }

  img#phoenix:hover {
    animation: none !important;
  }

  /* Disable brand logo animations for reduced motion */
  .brand-logo img {
    animation: none !important;
  }

  .brand-logo:hover {
    transform: none !important;
  }
}
