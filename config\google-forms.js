// This file only handles reCAPTCHA v3 integration

// Google reCAPTCHA v3 Configuration (loaded from PHP config)
if (typeof window.RECAPTCHA_CONFIG === 'undefined') {
  window.RECAPTCHA_CONFIG = {
    // Configuration will be set from PHP via environment variables
    enabled: false, // Will be set from server config
    version: 'v3',
    siteKey: '', // Will be set from server config

    // reCAPTCHA v3 settings
    action: 'contact_form', // Action name for v3
    scoreThreshold: 0.5 // Minimum score (0.0 to 1.0)
  };
}


/**
 * Initialize reCAPTCHA v3 protection
 */
function initializeRecaptcha() {
  if (window.RECAPTCHA_CONFIG.enabled && window.RECAPTCHA_CONFIG.siteKey) {
    console.log('🛡️ reCAPTCHA v3 protection enabled for contact form');

    // Show reCAPTCHA protection badge
    const recaptchaBadge = document.querySelector('#recaptcha-badge');
    if (recaptchaBadge) {
      recaptchaBadge.style.display = 'block';
    }

    // Show reCAPTCHA privacy notice
    const recaptchaNotice = document.querySelector('#recaptcha-notice');
    if (recaptchaNotice) {
      recaptchaNotice.style.display = 'block';
    }
  } else {
    console.error('❌ reCAPTCHA v3 not configured - contact form will not work');

    // Show error message
    const recaptchaContainer = document.querySelector('#recaptcha-container');
    if (recaptchaContainer) {
      recaptchaContainer.innerHTML = '<div style="color: #dc3545; font-weight: bold;">❌ reCAPTCHA not configured - Contact form disabled</div>';
    }
  }
}

/**
 * Validate reCAPTCHA v3
 */
function validateRecaptcha() {
  return new Promise((resolve) => {
    if (!window.RECAPTCHA_CONFIG.enabled || !window.RECAPTCHA_CONFIG.siteKey) {
      console.error('reCAPTCHA not configured - validation required');
      resolve(false);
      return;
    }

    if (typeof grecaptcha === 'undefined') {
      console.error('reCAPTCHA not loaded - validation failed');
      resolve(false);
      return;
    }

    // reCAPTCHA v3 validation
    grecaptcha.ready(() => {
      grecaptcha.execute(window.RECAPTCHA_CONFIG.siteKey, { action: window.RECAPTCHA_CONFIG.action })
        .then((token) => {
          resolve(token && token.length > 0);
        })
        .catch((error) => {
          console.error('reCAPTCHA validation failed:', error);
          resolve(false);
        });
    });
  });
}

/**
 * Reset reCAPTCHA (for reCAPTCHA v3, this is a no-op since it's invisible)
 */
function resetRecaptcha() {
  // reCAPTCHA v3 is invisible and doesn't need explicit reset
  // This function exists for compatibility with the form handler
  console.log('🛡️ reCAPTCHA v3 reset (no action needed for invisible reCAPTCHA)');
}

/**
 * Get reCAPTCHA v3 token (for form submission)
 */
function getRecaptchaToken() {
  return new Promise((resolve, reject) => {
    if (!window.RECAPTCHA_CONFIG.enabled || !window.RECAPTCHA_CONFIG.siteKey) {
      console.error('reCAPTCHA not configured - token required');
      reject(new Error('reCAPTCHA not configured'));
      return;
    }

    if (typeof grecaptcha === 'undefined') {
      console.error('reCAPTCHA not loaded - token required');
      reject(new Error('reCAPTCHA not loaded'));
      return;
    }

    // reCAPTCHA v3 - execute and get token
    grecaptcha.ready(() => {
      grecaptcha.execute(window.RECAPTCHA_CONFIG.siteKey, { action: window.RECAPTCHA_CONFIG.action })
        .then((token) => {
          if (!token) {
            reject(new Error('reCAPTCHA token not generated'));
            return;
          }

          console.log('🛡️ reCAPTCHA v3 token generated successfully');

          // Show brief success indicator
          const recaptchaBadge = document.querySelector('#recaptcha-badge');
          if (recaptchaBadge) {
            const originalText = recaptchaBadge.innerHTML;
            recaptchaBadge.innerHTML = '✅ reCAPTCHA verified - Form ready to submit';
            recaptchaBadge.style.background = 'rgba(40,167,69,0.1)';
            recaptchaBadge.style.borderColor = 'rgba(40,167,69,0.3)';
            recaptchaBadge.style.color = '#28a745';

            // Reset after 2 seconds
            setTimeout(() => {
              recaptchaBadge.innerHTML = originalText;
              recaptchaBadge.style.background = 'rgba(108,99,255,0.1)';
              recaptchaBadge.style.borderColor = 'rgba(108,99,255,0.3)';
              recaptchaBadge.style.color = 'var(--accent)';
            }, 2000);
          }

          resolve(token);
        })
        .catch((error) => {
          console.error('reCAPTCHA token generation failed:', error);
          reject(error);
        });
    });
  });
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeRecaptcha);
} else {
  initializeRecaptcha();
}

// Export reCAPTCHA functions for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    RECAPTCHA_CONFIG: window.RECAPTCHA_CONFIG,
    initializeRecaptcha,
    validateRecaptcha,
    resetRecaptcha,
    getRecaptchaToken
  };
} else if (typeof window !== 'undefined') {
  // Export reCAPTCHA integration functions
  window.initializeRecaptcha = initializeRecaptcha;
  window.GoogleFormsIntegration = {
    validateRecaptcha,
    resetRecaptcha,
    getRecaptchaToken
  };
}
